<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title"><PERSON><PERSON><PERSON></title>
    <style>
        :root {
            --primary-color: #4a86e8;
            --secondary-color: #6fa8f5;
            --success-color: #34a853;
            --warning-color: #fbbc04;
            --danger-color: #ea4335;
            --task-color: #e8f4f8;
            --free-time-color: #d9ead3;
            --homework-color: #fff2cc;
            --chores-color: #f4cccc;
            --completed-color: #c8e6c9;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }

        .mode-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .mode-toggle:hover {
            background: white;
            color: var(--primary-color);
        }

        .mode-toggle.admin-mode {
            background: rgba(255, 193, 7, 0.9);
            border-color: #ffc107;
            color: #212529;
            font-weight: 600;
        }

        .mode-toggle.admin-mode:hover {
            background: #ffc107;
            color: #212529;
        }

        .mode-indicator {
            font-size: 0.8em;
            opacity: 0.9;
        }

        .admin-only {
            display: block;
        }

        .user-mode .admin-only {
            display: none !important;
        }

        .user-mode .activity-actions,
        .user-mode .responsibility-actions {
            display: none !important;
        }

        .user-mode .add-activity-btn {
            display: none !important;
        }

        .content {
            padding: 30px;
        }

        .schedule-container {
            margin-bottom: 30px;
        }

        /* Timeline Grid Layout */
        .timeline-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .day-timeline {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .day-timeline:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .day-header {
            background: var(--primary-color);
            color: white;
            padding: 15px 20px;
            text-align: center;
            font-weight: 600;
            font-size: 1.1em;
        }

        .day-activities {
            padding: 15px;
        }

        .time-activity {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .time-activity:hover {
            transform: scale(1.02);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .time-slot-timeline {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            font-size: 0.9em;
            padding: 8px 12px;
            border-radius: 6px;
            min-width: 90px;
            text-align: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .activity-text {
            flex: 1;
            text-align: left;
            font-weight: 500;
        }

        .activity-actions {
            display: none;
            gap: 5px;
            margin-left: 10px;
        }

        .time-activity:hover .activity-actions {
            display: flex;
        }

        .time-activity.completed {
            background: var(--completed-color) !important;
            text-decoration: line-through;
            opacity: 0.8;
        }

        .time-activity.completed::after {
            content: "✓";
            position: absolute;
            top: 8px;
            right: 8px;
            color: var(--success-color);
            font-weight: bold;
            font-size: 1.2em;
        }

        .btn-edit, .btn-remove {
            padding: 2px 6px;
            font-size: 0.7em;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-edit {
            background: #ffc107;
            color: #212529;
        }

        .btn-edit:hover {
            background: #e0a800;
        }

        .btn-remove {
            background: var(--danger-color);
            color: white;
        }

        .btn-remove:hover {
            background: #d32f2f;
        }

        /* Activity type colors for timeline */
        .time-activity.task { background-color: var(--task-color); }
        .time-activity.free-time { background-color: var(--free-time-color); }
        .time-activity.homework { background-color: var(--homework-color); }
        .time-activity.chores { background-color: var(--chores-color); }

        /* Empty day message */
        .empty-day {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
            font-style: italic;
        }

        .empty-day-icon {
            font-size: 2em;
            margin-bottom: 10px;
            opacity: 0.5;
        }

        .responsibilities {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }

        .responsibilities h2 {
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .responsibility-list {
            list-style: none;
            padding: 0;
        }

        .responsibility-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
            position: relative;
        }

        .responsibility-content {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .responsibility-actions {
            display: none;
            gap: 5px;
            margin-left: 15px;
        }

        .responsibility-item:hover .responsibility-actions {
            display: flex;
        }

        .responsibility-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }

        .responsibility-item.completed {
            background: var(--completed-color);
            text-decoration: line-through;
            opacity: 0.8;
        }

        .responsibility-checkbox {
            width: 20px;
            height: 20px;
            margin-right: 15px;
            cursor: pointer;
        }

        .add-activity-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .add-activity-modal.active {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .modal-title {
            font-size: 1.5em;
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            color: #6c757d;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-modal:hover {
            background: #f8f9fa;
            color: var(--danger-color);
        }

        .add-activity-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(74, 134, 232, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
        }

        .add-activity-btn:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(74, 134, 232, 0.6);
        }

        .form-group {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
        }

        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #495057;
            font-size: 1.1em;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 134, 232, 0.1);
        }

        .time-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .days-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .day-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .day-checkbox:hover {
            border-color: var(--primary-color);
            background: #f8f9fa;
        }

        .day-checkbox.selected {
            border-color: var(--primary-color);
            background: rgba(74, 134, 232, 0.1);
            color: var(--primary-color);
            font-weight: 600;
        }

        .day-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin: 0;
        }

        .activity-type-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .type-option {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .type-option:hover {
            border-color: var(--primary-color);
        }

        .type-option.selected {
            border-color: var(--primary-color);
            background: rgba(74, 134, 232, 0.1);
        }

        .type-option input[type="radio"] {
            width: 18px;
            height: 18px;
            margin: 0;
        }

        .conflict-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            display: none;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .conflict-warning.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .conflict-warning h4 {
            color: #856404;
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }

        .conflict-list {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }

        .conflict-list li {
            background: white;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .schedule-edit-grid {
            display: grid;
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }

        .time-slot-editor {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
            position: relative;
        }

        .time-slot-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .time-slot-input {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1.1em;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 5px 10px;
            background: white;
            min-width: 150px;
        }

        .time-slot-controls {
            display: flex;
            gap: 5px;
            margin-left: auto;
        }

        .btn-time {
            padding: 4px 8px;
            font-size: 0.8em;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-add-time {
            background: var(--success-color);
            color: white;
        }

        .btn-add-time:hover {
            background: #2d8f47;
        }

        .btn-remove-time {
            background: var(--danger-color);
            color: white;
        }

        .btn-remove-time:hover {
            background: #d32f2f;
        }

        .day-activities {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .day-activity-editor {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .day-activity-editor label {
            font-size: 0.9em;
            font-weight: 600;
            color: #6c757d;
        }

        .day-activity-editor input {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .day-activity-editor select {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .responsibility-editor {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: white;
            margin-bottom: 10px;
        }

        .responsibility-editor input {
            margin-bottom: 8px;
        }

        .add-remove-buttons {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .btn-small {
            padding: 5px 10px;
            font-size: 0.9em;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #d32f2f;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #2d8f47;
            transform: translateY(-2px);
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, var(--success-color), #4caf50);
            height: 100%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
            color: #495057;
        }

        /* Password Modal Styles */
        .password-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }

        .password-modal.active {
            display: flex;
        }

        .password-modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        .password-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1.1em;
            margin: 15px 0;
            text-align: center;
        }

        .password-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 134, 232, 0.1);
        }

        /* Print Button Styles */
        .print-button {
            position: fixed;
            bottom: 100px;
            right: 30px;
            background: var(--success-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(52, 168, 83, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .print-button:hover {
            background: #2d8f47;
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(52, 168, 83, 0.6);
        }

        /* Print-specific elements */
        .print-only {
            display: none;
        }

        @media print {
            .print-only {
                display: block !important;
            }

            /* Print header with additional info */
            .print-header {
                text-align: center;
                margin-bottom: 20px;
                padding: 10px;
                border-bottom: 2px solid #333;
                font-size: 10pt;
                color: #666;
            }

            /* Hide all interactive elements and UI controls */
            .mode-toggle,
            .add-activity-btn,
            .print-button,
            .activity-actions,
            .responsibility-actions,
            .btn-edit,
            .btn-remove,
            .add-activity-modal,
            .edit-responsibility-modal,
            .password-modal {
                display: none !important;
            }

            /* Reset page layout for print */
            body {
                background: white !important;
                margin: 0;
                padding: 0;
                font-size: 12pt;
                line-height: 1.4;
                color: black !important;
            }

            .container {
                max-width: none;
                margin: 0;
                background: white !important;
                border-radius: 0;
                box-shadow: none;
                overflow: visible;
            }

            .header {
                background: white !important;
                color: black !important;
                padding: 20px 0;
                text-align: center;
                border-bottom: 2px solid #333;
                margin-bottom: 20px;
            }

            .header h1 {
                font-size: 24pt;
                margin: 0;
                text-shadow: none;
                color: black !important;
            }

            .header .subtitle {
                font-size: 14pt;
                margin: 10px 0 0 0;
                opacity: 1;
                color: #666 !important;
            }

            .content {
                padding: 0 20px;
            }

            /* Progress bar - simplified for print */
            .progress-container h3 {
                color: black !important;
                font-size: 16pt;
                margin-bottom: 10px;
            }

            .progress-bar {
                border: 1px solid #333;
                background: white !important;
                height: 15px;
            }

            .progress-fill {
                background: #333 !important;
                print-color-adjust: exact;
            }

            .progress-text {
                color: black !important;
                font-size: 12pt;
            }

            /* Timeline grid - optimized for print layout */
            .timeline-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
                margin: 20px 0;
                page-break-inside: avoid;
            }

            .day-timeline {
                background: white !important;
                border: 1px solid #333;
                border-radius: 0;
                box-shadow: none;
                page-break-inside: avoid;
                margin-bottom: 15px;
            }

            .day-header {
                background: #f0f0f0 !important;
                color: black !important;
                padding: 10px;
                text-align: center;
                font-weight: bold;
                font-size: 14pt;
                border-bottom: 1px solid #333;
            }

            .day-activities {
                padding: 10px;
            }

            .time-activity {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 0;
                background: white !important;
                page-break-inside: avoid;
            }

            /* Activity type backgrounds for print */
            .time-activity.task {
                background: #f8f8f8 !important;
                print-color-adjust: exact;
            }

            .time-activity.free-time {
                background: #f0f8f0 !important;
                print-color-adjust: exact;
            }

            .time-activity.homework {
                background: #fff8e0 !important;
                print-color-adjust: exact;
            }

            .time-activity.chores {
                background: #f8f0f0 !important;
                print-color-adjust: exact;
            }

            .time-activity.completed {
                background: #e8f5e8 !important;
                text-decoration: line-through;
                print-color-adjust: exact;
            }

            .time-activity.completed::after {
                content: "✓";
                position: absolute;
                right: 8px;
                color: #333 !important;
                font-weight: bold;
            }

            .time-slot-timeline {
                background: #e8e8e8 !important;
                color: black !important;
                font-weight: bold;
                font-size: 10pt;
                padding: 6px 8px;
                border: 1px solid #ccc;
                min-width: 80px;
                text-align: center;
                margin-right: 10px;
                print-color-adjust: exact;
            }

            .activity-text {
                font-size: 11pt;
                color: black !important;
                font-weight: normal;
            }

            .empty-day {
                text-align: center;
                padding: 20px;
                color: #666 !important;
                font-style: italic;
                font-size: 11pt;
            }

            /* Responsibilities section */
            .responsibilities {
                background: white !important;
                border: 1px solid #333;
                border-radius: 0;
                padding: 15px;
                margin-top: 20px;
                page-break-inside: avoid;
            }

            .responsibilities h2 {
                color: black !important;
                font-size: 16pt;
                margin-top: 0;
                margin-bottom: 15px;
                border-bottom: 1px solid #333;
                padding-bottom: 5px;
            }

            .responsibility-item {
                background: white !important;
                margin: 8px 0;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 0;
                box-shadow: none;
                display: flex;
                align-items: center;
                page-break-inside: avoid;
            }

            .responsibility-item.completed {
                background: #f0f0f0 !important;
                text-decoration: line-through;
                print-color-adjust: exact;
            }

            .responsibility-checkbox {
                width: 15px;
                height: 15px;
                margin-right: 10px;
                print-color-adjust: exact;
            }

            .responsibility-content label {
                font-size: 11pt;
                color: black !important;
            }

            /* Instructions panel */
            #instructions-panel {
                background: #f8f8f8 !important;
                border: 1px solid #333;
                padding: 15px;
                border-radius: 0;
                margin-bottom: 20px;
                print-color-adjust: exact;
            }

            #instructions-panel h3 {
                color: black !important;
                font-size: 14pt;
                margin-top: 0;
            }

            #instructions-panel p {
                color: black !important;
                font-size: 11pt;
            }

            /* Page breaks */
            .responsibilities {
                page-break-before: auto;
            }

            /* Print legend for activity types */
            .print-legend {
                margin: 15px 0;
                padding: 10px;
                border: 1px solid #333;
                background: #f8f8f8 !important;
                page-break-inside: avoid;
                print-color-adjust: exact;
            }

            .print-legend h4 {
                margin: 0 0 10px 0;
                font-size: 12pt;
                color: black !important;
            }

            .legend-items {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 5px;
                font-size: 10pt;
            }

            .legend-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .legend-color {
                width: 15px;
                height: 15px;
                border: 1px solid #333;
                print-color-adjust: exact;
            }

            .legend-color.task { background: #f8f8f8 !important; }
            .legend-color.free-time { background: #f0f8f0 !important; }
            .legend-color.homework { background: #fff8e0 !important; }
            .legend-color.chores { background: #f8f0f0 !important; }

            /* Ensure good contrast for printing */
            * {
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .content {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .timeline-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .day-timeline {
                margin-bottom: 10px;
            }

            .time-activity {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .time-slot-timeline {
                margin-right: 0;
                margin-bottom: 5px;
                min-width: auto;
                width: fit-content;
            }

            .activity-content {
                width: 100%;
            }

            .mode-toggle {
                position: static;
                margin-top: 15px;
                display: block;
            }

            .print-button {
                bottom: 80px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 1.2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 id="main-title">Orarul Copiilor</h1>
            <p class="subtitle" id="subtitle">Programul zilnic pentru copii</p>
            <button class="mode-toggle" id="mode-toggle" onclick="toggleMode()">
                <span id="mode-icon">👤</span>
                <span id="mode-text">Mod Utilizator</span>
            </button>
        </div>

        <div class="content">
            <!-- Print-only header with additional information -->
            <div class="print-only print-header">
                <p><strong>Orar tipărit pe:</strong> <span id="print-date"></span></p>
                <p><em>Pentru actualizări și modificări, accesați versiunea digitală</em></p>
            </div>

            <!-- Instructions Panel -->
            <div id="instructions-panel" style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin-bottom: 20px; display: none;">
                <h3 style="margin-top: 0; color: var(--primary-color);">📋 Instrucțiuni Speciale</h3>
                <p id="custom-instructions"></p>
            </div>

            <!-- Progress Bar -->
            <div class="progress-container">
                <h3 style="color: var(--primary-color); margin-bottom: 10px;">📊 Progresul de Astăzi</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                </div>
                <div class="progress-text" id="progress-text">0% completat</div>
            </div>

            <!-- Print-only legend for activity types -->
            <div class="print-only print-legend">
                <h4>🎨 Legenda Tipurilor de Activități:</h4>
                <div class="legend-items">
                    <div class="legend-item">
                        <div class="legend-color task"></div>
                        <span>Activități de bază</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color free-time"></div>
                        <span>Timp liber</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color homework"></div>
                        <span>Școală/Teme</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color chores"></div>
                        <span>Treburi casnice</span>
                    </div>
                </div>
            </div>

            <!-- Schedule Timeline Container -->
            <div class="schedule-container">
                <div class="timeline-grid" id="timeline-grid">
                    <!-- Independent day timelines will be generated by JavaScript -->
                </div>
            </div>

            <!-- Responsibilities Section -->
            <div class="responsibilities">
                <h2>🏠 Responsabilități Casnice</h2>
                <ul class="responsibility-list" id="responsibility-list">
                    <!-- Responsibilities will be generated by JavaScript -->
                </ul>
            </div>
        </div>

        <!-- Add Activity Button -->
        <button class="add-activity-btn" onclick="openAddActivityModal()" title="Adaugă Activitate Nouă">
            ➕
        </button>

        <!-- Print Button -->
        <button class="print-button" onclick="printSchedule()" title="Tipărește Orarul">
            🖨️
        </button>

        <!-- Add Activity Modal -->
        <div id="add-activity-modal" class="add-activity-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title" id="activity-modal-title">➕ Adaugă Activitate Nouă</h2>
                    <button class="close-modal" onclick="closeAddActivityModal()">✕</button>
                </div>

                <form id="add-activity-form">
                    <!-- Activity Name -->
                    <div class="form-group">
                        <label for="activity-name">📝 Numele Activității</label>
                        <input type="text" id="activity-name" placeholder="ex: Micul dejun, Teme, Timp de joacă..." required>
                    </div>

                    <!-- Time Selection -->
                    <div class="form-group">
                        <label>⏰ Intervalul Orar</label>
                        <div class="time-inputs">
                            <div>
                                <label for="start-time" style="font-size: 0.9em; color: #6c757d;">Ora de început</label>
                                <input type="time" id="start-time" required>
                            </div>
                            <div>
                                <label for="end-time" style="font-size: 0.9em; color: #6c757d;">Ora de sfârșit</label>
                                <input type="time" id="end-time" required>
                            </div>
                        </div>
                    </div>

                    <!-- Conflict Warning -->
                    <div id="conflict-warning" class="conflict-warning">
                        <h4>⚠️ Conflict de Program Detectat</h4>
                        <p>Intervalul orar selectat se suprapune cu următoarele activități:</p>
                        <ul id="conflict-list" class="conflict-list"></ul>
                        <p><strong>🔄 Dacă continui, activitățile conflictuale vor fi ȘTERSE AUTOMAT și înlocuite cu noua activitate.</strong></p>
                        <p style="font-size: 0.9em; color: #856404; margin-top: 10px;">
                            <em>💡 Tip: Dacă activitatea existentă se extinde în afara intervalului nou, părțile care nu se suprapun vor fi păstrate ca activități separate.</em>
                        </p>
                    </div>

                    <!-- Day Selection -->
                    <div class="form-group">
                        <label>📅 Selectează Zilele</label>
                        <div class="days-selection">
                            <label class="day-checkbox" for="day-monday">
                                <input type="checkbox" id="day-monday" value="Luni">
                                <span>Luni</span>
                            </label>
                            <label class="day-checkbox" for="day-tuesday">
                                <input type="checkbox" id="day-tuesday" value="Marți">
                                <span>Marți</span>
                            </label>
                            <label class="day-checkbox" for="day-wednesday">
                                <input type="checkbox" id="day-wednesday" value="Miercuri">
                                <span>Miercuri</span>
                            </label>
                            <label class="day-checkbox" for="day-thursday">
                                <input type="checkbox" id="day-thursday" value="Joi">
                                <span>Joi</span>
                            </label>
                            <label class="day-checkbox" for="day-friday">
                                <input type="checkbox" id="day-friday" value="Vineri">
                                <span>Vineri</span>
                            </label>
                            <label class="day-checkbox" for="day-saturday">
                                <input type="checkbox" id="day-saturday" value="Sâmbătă">
                                <span>Sâmbătă</span>
                            </label>
                            <label class="day-checkbox" for="day-sunday">
                                <input type="checkbox" id="day-sunday" value="Duminică">
                                <span>Duminică</span>
                            </label>
                        </div>
                    </div>

                    <!-- Activity Type -->
                    <div class="form-group">
                        <label>🎨 Tipul Activității</label>
                        <div class="activity-type-selection">
                            <label class="type-option" for="type-task">
                                <input type="radio" id="type-task" name="activity-type" value="task" checked>
                                <span>📋 Activități de bază</span>
                            </label>
                            <label class="type-option" for="type-free-time">
                                <input type="radio" id="type-free-time" name="activity-type" value="free-time">
                                <span>🎮 Timp liber</span>
                            </label>
                            <label class="type-option" for="type-homework">
                                <input type="radio" id="type-homework" name="activity-type" value="homework">
                                <span>📚 Școală/Teme</span>
                            </label>
                            <label class="type-option" for="type-chores">
                                <input type="radio" id="type-chores" name="activity-type" value="chores">
                                <span>🏠 Treburi casnice</span>
                            </label>
                        </div>
                    </div>

                    <!-- Modal Actions -->
                    <div class="modal-actions">
                        <button type="button" class="btn btn-danger" onclick="closeAddActivityModal()">❌ Anulează</button>
                        <button type="submit" class="btn btn-primary" id="activity-submit-btn">💾 Salvează Activitatea</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Edit Responsibility Modal -->
        <div id="edit-responsibility-modal" class="add-activity-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title">✏️ Editează Responsabilitatea</h2>
                    <button class="close-modal" onclick="closeEditResponsibilityModal()">✕</button>
                </div>

                <form id="edit-responsibility-form">
                    <input type="hidden" id="edit-resp-index">

                    <!-- Responsibility Text -->
                    <div class="form-group">
                        <label for="edit-resp-text">📝 Numele Responsabilității</label>
                        <input type="text" id="edit-resp-text" placeholder="ex: Aranjarea patului, Strângerea jucăriilor..." required>
                    </div>

                    <!-- Frequency -->
                    <div class="form-group">
                        <label for="edit-resp-frequency">📅 Frecvența</label>
                        <select id="edit-resp-frequency" required>
                            <option value="zilnic">Zilnic</option>
                            <option value="săptămânal">Săptămânal</option>
                            <option value="conform programului">Conform programului</option>
                            <option value="la nevoie">La nevoie</option>
                        </select>
                    </div>

                    <!-- Type -->
                    <div class="form-group">
                        <label for="edit-resp-type">🏷️ Tipul</label>
                        <select id="edit-resp-type" required>
                            <option value="daily">Zilnic</option>
                            <option value="weekly">Săptămânal</option>
                            <option value="scheduled">Programat</option>
                            <option value="asneeded">La nevoie</option>
                        </select>
                    </div>

                    <!-- Modal Actions -->
                    <div class="modal-actions">
                        <button type="button" class="btn btn-danger" onclick="closeEditResponsibilityModal()">❌ Anulează</button>
                        <button type="submit" class="btn btn-primary">💾 Salvează Modificările</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Password Modal -->
        <div id="password-modal" class="password-modal">
            <div class="password-modal-content">
                <h2 style="color: var(--primary-color); margin-bottom: 20px;">🔐 Acces Administrator</h2>
                <p style="color: #6c757d; margin-bottom: 20px;">Introdu parola pentru a accesa modul administrator:</p>
                <input type="password" id="password-input" class="password-input" placeholder="Parola..." maxlength="20">
                <div style="display: flex; gap: 15px; justify-content: center; margin-top: 20px;">
                    <button class="btn btn-danger" onclick="closePasswordModal()">❌ Anulează</button>
                    <button class="btn btn-primary" onclick="checkPassword()">🔓 Acces</button>
                </div>
                <p style="font-size: 0.8em; color: #6c757d; margin-top: 15px;">
                    <em>Parola implicită: "admin"</em>
                </p>
            </div>
        </div>
    </div>

    <script>
        // 🎯 CONFIGURATION SYSTEM - Easy to customize!
        const CONFIG = {
            // Basic Settings
            title: "Orarul Copiilor",
            subtitle: "Programul zilnic pentru copii",
            language: "ro", // ro, en, etc.

            // Custom Instructions (shown when not empty)
            customInstructions: "",

            // Days of the week
            days: ["Luni", "Marți", "Miercuri", "Joi", "Vineri", "Sâmbătă", "Duminică"],

            // Day-specific schedules - Each day has its own independent time slots and activities
            daySchedules: {
                "Luni": {
                    timeSlots: [
                        { time: "7:00 - 8:00", activity: { text: "Trezire, Micul dejun", type: "task" } },
                        { time: "8:00 - 9:00", activity: { text: "Pregătire pentru școală", type: "task" } },
                        { time: "9:00 - 12:00", activity: { text: "Școala", type: "homework" } },
                        { time: "12:00 - 13:00", activity: { text: "Prânz", type: "task" } },
                        { time: "15:00 - 16:00", activity: { text: "Teme", type: "homework" } },
                        { time: "17:00 - 18:00", activity: { text: "Treburi casnice", type: "chores" } },
                        { time: "19:00 - 20:00", activity: { text: "Cină", type: "task" } },
                        { time: "20:00 - 21:00", activity: { text: "Pregătire pentru culcare", type: "task" } }
                    ]
                },
                "Marți": {
                    timeSlots: [
                        { time: "7:00 - 8:00", activity: { text: "Trezire, Micul dejun", type: "task" } },
                        { time: "8:00 - 9:00", activity: { text: "Pregătire pentru școală", type: "task" } },
                        { time: "9:00 - 12:00", activity: { text: "Școala", type: "homework" } },
                        { time: "12:00 - 13:00", activity: { text: "Prânz", type: "task" } },
                        { time: "15:00 - 16:00", activity: { text: "Teme", type: "homework" } },
                        { time: "17:00 - 18:00", activity: { text: "Treburi casnice", type: "chores" } },
                        { time: "19:00 - 20:00", activity: { text: "Cină", type: "task" } },
                        { time: "20:00 - 21:00", activity: { text: "Pregătire pentru culcare", type: "task" } }
                    ]
                },
                "Miercuri": {
                    timeSlots: [
                        { time: "7:00 - 8:00", activity: { text: "Trezire, Micul dejun", type: "task" } },
                        { time: "8:00 - 9:00", activity: { text: "Pregătire pentru școală", type: "task" } },
                        { time: "9:00 - 12:00", activity: { text: "Școala", type: "homework" } },
                        { time: "12:00 - 13:00", activity: { text: "Prânz", type: "task" } },
                        { time: "15:00 - 16:00", activity: { text: "Teme", type: "homework" } },
                        { time: "17:00 - 18:00", activity: { text: "Treburi casnice", type: "chores" } },
                        { time: "19:00 - 20:00", activity: { text: "Cină", type: "task" } },
                        { time: "20:00 - 21:00", activity: { text: "Pregătire pentru culcare", type: "task" } }
                    ]
                },
                "Joi": {
                    timeSlots: [
                        { time: "7:00 - 8:00", activity: { text: "Trezire, Micul dejun", type: "task" } },
                        { time: "8:00 - 9:00", activity: { text: "Pregătire pentru școală", type: "task" } },
                        { time: "9:00 - 12:00", activity: { text: "Școala", type: "homework" } },
                        { time: "12:00 - 13:00", activity: { text: "Prânz", type: "task" } },
                        { time: "15:00 - 16:00", activity: { text: "Teme", type: "homework" } },
                        { time: "17:00 - 18:00", activity: { text: "Treburi casnice", type: "chores" } },
                        { time: "19:00 - 20:00", activity: { text: "Cină", type: "task" } },
                        { time: "20:00 - 21:00", activity: { text: "Pregătire pentru culcare", type: "task" } }
                    ]
                },
                "Vineri": {
                    timeSlots: [
                        { time: "7:00 - 8:00", activity: { text: "Trezire, Micul dejun", type: "task" } },
                        { time: "8:00 - 9:00", activity: { text: "Pregătire pentru școală", type: "task" } },
                        { time: "9:00 - 12:00", activity: { text: "Școala", type: "homework" } },
                        { time: "12:00 - 13:00", activity: { text: "Prânz", type: "task" } },
                        { time: "15:00 - 16:00", activity: { text: "Timp liber", type: "free-time" } },
                        { time: "17:00 - 18:00", activity: { text: "Treburi casnice", type: "chores" } },
                        { time: "19:00 - 20:00", activity: { text: "Cină", type: "task" } },
                        { time: "20:00 - 22:00", activity: { text: "Timp liber", type: "free-time" } }
                    ]
                },
                "Sâmbătă": {
                    timeSlots: [
                        { time: "8:00 - 9:00", activity: { text: "Somn prelungit", type: "free-time" } },
                        { time: "9:00 - 10:00", activity: { text: "Micul dejun", type: "task" } },
                        { time: "10:00 - 12:00", activity: { text: "Timp liber", type: "free-time" } },
                        { time: "12:00 - 13:00", activity: { text: "Prânz", type: "task" } },
                        { time: "15:00 - 16:00", activity: { text: "Activități", type: "free-time" } },
                        { time: "17:00 - 18:00", activity: { text: "Treburi casnice", type: "chores" } },
                        { time: "19:00 - 20:00", activity: { text: "Cină", type: "task" } },
                        { time: "20:00 - 22:00", activity: { text: "Timp liber", type: "free-time" } }
                    ]
                },
                "Duminică": {
                    timeSlots: [
                        { time: "8:00 - 9:00", activity: { text: "Somn prelungit", type: "free-time" } },
                        { time: "9:00 - 10:00", activity: { text: "Micul dejun", type: "task" } },
                        { time: "10:00 - 12:00", activity: { text: "Timp liber", type: "free-time" } },
                        { time: "12:00 - 13:00", activity: { text: "Prânz", type: "task" } },
                        { time: "15:00 - 16:00", activity: { text: "Timp liber", type: "free-time" } },
                        { time: "17:00 - 18:00", activity: { text: "Pregătire săptămână", type: "chores" } },
                        { time: "19:00 - 20:00", activity: { text: "Cină", type: "task" } },
                        { time: "20:00 - 21:00", activity: { text: "Pregătire pentru culcare", type: "task" } }
                    ]
                }
            },

            // Household responsibilities - EASY TO CUSTOMIZE!
            responsibilities: [
                { text: "Aranjarea patului", frequency: "zilnic", type: "daily" },
                { text: "Strângerea jucăriilor", frequency: "zilnic", type: "daily" },
                { text: "Ajutor la masă", frequency: "conform programului", type: "scheduled" },
                { text: "Hrănirea animalelor de companie", frequency: "zilnic", type: "daily" },
                { text: "Sortarea hainelor murdare", frequency: "săptămânal", type: "weekly" },
                { text: "Curățarea camerei", frequency: "săptămânal", type: "weekly" },
                { text: "Ajutor la cumpărături", frequency: "la nevoie", type: "asneeded" }
            ],

            // Activity types and their colors
            activityTypes: {
                "task": { name: "Activități de bază", color: "#e8f4f8" },
                "free-time": { name: "Timp liber", color: "#d9ead3" },
                "homework": { name: "Școală/Teme", color: "#fff2cc" },
                "chores": { name: "Treburi casnice", color: "#f4cccc" }
            }
        };

        // 🔧 CORE FUNCTIONALITY
        let completedActivities = new Set();
        let completedResponsibilities = new Set();

        // Mode management
        let isAdminMode = false;
        const ADMIN_PASSWORD = "admin"; // Simple password - can be changed

        // Initialize the application
        function initApp() {
            loadSettings();
            loadMode();
            generateSchedule();
            generateResponsibilities();
            updateProgress();
            loadProgress();
            updateModeDisplay();
        }

        // Generate independent timelines for each day
        function generateSchedule() {
            const timelineGrid = document.getElementById('timeline-grid');
            timelineGrid.innerHTML = '';

            // Create a timeline for each day
            CONFIG.days.forEach(day => {
                const dayTimeline = document.createElement('div');
                dayTimeline.className = 'day-timeline';

                // Day header
                const dayHeader = document.createElement('div');
                dayHeader.className = 'day-header';
                dayHeader.textContent = day;
                dayTimeline.appendChild(dayHeader);

                // Day activities container
                const dayActivities = document.createElement('div');
                dayActivities.className = 'day-activities';

                // Get activities for this day
                const daySchedule = CONFIG.daySchedules[day];

                if (daySchedule && daySchedule.timeSlots && daySchedule.timeSlots.length > 0) {
                    // Sort activities by time
                    const sortedActivities = [...daySchedule.timeSlots].sort((a, b) => {
                        const timeA = timeToMinutes(a.time.split(' - ')[0]);
                        const timeB = timeToMinutes(b.time.split(' - ')[0]);
                        return timeA - timeB;
                    });

                    // Create activity elements
                    sortedActivities.forEach(slot => {
                        const timeActivity = document.createElement('div');
                        timeActivity.className = `time-activity ${slot.activity.type}`;

                        // Time slot
                        const timeSlotElement = document.createElement('div');
                        timeSlotElement.className = 'time-slot-timeline';
                        timeSlotElement.textContent = slot.time;

                        // Activity content
                        const activityContent = document.createElement('div');
                        activityContent.className = 'activity-content';

                        const activityText = document.createElement('div');
                        activityText.className = 'activity-text';
                        activityText.textContent = slot.activity.text;

                        const activityActions = document.createElement('div');
                        activityActions.className = 'activity-actions';

                        const editBtn = document.createElement('button');
                        editBtn.className = 'btn-edit';
                        editBtn.innerHTML = '✏️';
                        editBtn.title = 'Editează';
                        editBtn.onclick = (e) => {
                            e.stopPropagation();
                            openEditActivityModal(slot.time, day, slot.activity);
                        };

                        const removeBtn = document.createElement('button');
                        removeBtn.className = 'btn-remove';
                        removeBtn.innerHTML = '🗑️';
                        removeBtn.title = 'Șterge';
                        removeBtn.onclick = (e) => {
                            e.stopPropagation();
                            removeActivity(slot.time, day);
                        };

                        activityActions.appendChild(editBtn);
                        activityActions.appendChild(removeBtn);

                        activityContent.appendChild(activityText);
                        activityContent.appendChild(activityActions);

                        timeActivity.appendChild(timeSlotElement);
                        timeActivity.appendChild(activityContent);

                        // Add click handler for completion toggle
                        timeActivity.onclick = () => toggleActivityCompletion(slot.time, day, timeActivity);

                        // Check if already completed
                        const activityId = `${slot.time}-${day}`;
                        if (completedActivities.has(activityId)) {
                            timeActivity.classList.add('completed');
                        }

                        dayActivities.appendChild(timeActivity);
                    });
                } else {
                    // Empty day message
                    const emptyDay = document.createElement('div');
                    emptyDay.className = 'empty-day';
                    emptyDay.innerHTML = `
                        <div class="empty-day-icon">📅</div>
                        <div>Nu sunt activități programate</div>
                    `;
                    dayActivities.appendChild(emptyDay);
                }

                dayTimeline.appendChild(dayActivities);
                timelineGrid.appendChild(dayTimeline);
            });
        }

        // Generate responsibilities list
        function generateResponsibilities() {
            const list = document.getElementById('responsibility-list');
            list.innerHTML = '';

            CONFIG.responsibilities.forEach((responsibility, index) => {
                const item = document.createElement('li');
                item.className = 'responsibility-item';

                const responsibilityContent = document.createElement('div');
                responsibilityContent.className = 'responsibility-content';

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'responsibility-checkbox';
                checkbox.id = `resp-${index}`;
                checkbox.onchange = () => toggleResponsibilityCompletion(index, item);

                const label = document.createElement('label');
                label.htmlFor = `resp-${index}`;
                label.innerHTML = `<strong>${responsibility.text}</strong> - ${responsibility.frequency}`;

                responsibilityContent.appendChild(checkbox);
                responsibilityContent.appendChild(label);

                const responsibilityActions = document.createElement('div');
                responsibilityActions.className = 'responsibility-actions';

                const editBtn = document.createElement('button');
                editBtn.className = 'btn-edit btn-small';
                editBtn.innerHTML = '✏️';
                editBtn.title = 'Editează';
                editBtn.onclick = () => openEditResponsibilityModal(index);

                const removeBtn = document.createElement('button');
                removeBtn.className = 'btn-remove btn-small';
                removeBtn.innerHTML = '🗑️';
                removeBtn.title = 'Șterge';
                removeBtn.onclick = () => removeResponsibility(index);

                responsibilityActions.appendChild(editBtn);
                responsibilityActions.appendChild(removeBtn);

                // Check if already completed
                if (completedResponsibilities.has(index.toString())) {
                    checkbox.checked = true;
                    item.classList.add('completed');
                }

                item.appendChild(responsibilityContent);
                item.appendChild(responsibilityActions);
                list.appendChild(item);
            });
        }

        // Toggle activity completion
        function toggleActivityCompletion(timeSlot, day, element) {
            const activityId = `${timeSlot}-${day}`;

            if (completedActivities.has(activityId)) {
                completedActivities.delete(activityId);
                element.classList.remove('completed');
            } else {
                completedActivities.add(activityId);
                element.classList.add('completed');
            }

            updateProgress();
            saveProgress();
        }

        // Toggle responsibility completion
        function toggleResponsibilityCompletion(index, item) {
            const respId = index.toString();

            if (completedResponsibilities.has(respId)) {
                completedResponsibilities.delete(respId);
                item.classList.remove('completed');
            } else {
                completedResponsibilities.add(respId);
                item.classList.add('completed');
            }

            updateProgress();
            saveProgress();
        }

        // Update progress bar
        function updateProgress() {
            // Calculate total activities from day-specific schedules
            let totalActivities = 0;
            CONFIG.days.forEach(day => {
                if (CONFIG.daySchedules[day] && CONFIG.daySchedules[day].timeSlots) {
                    totalActivities += CONFIG.daySchedules[day].timeSlots.length;
                }
            });

            const totalResponsibilities = CONFIG.responsibilities.length;
            const total = totalActivities + totalResponsibilities;

            const completed = completedActivities.size + completedResponsibilities.size;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');

            progressFill.style.width = percentage + '%';
            progressText.textContent = `${percentage}% completat (${completed}/${total} activități)`;
        }

        // Save progress to localStorage
        function saveProgress() {
            const progress = {
                activities: Array.from(completedActivities),
                responsibilities: Array.from(completedResponsibilities),
                date: new Date().toDateString()
            };
            localStorage.setItem('childScheduleProgress', JSON.stringify(progress));
        }

        // Load progress from localStorage
        function loadProgress() {
            const saved = localStorage.getItem('childScheduleProgress');
            if (saved) {
                const progress = JSON.parse(saved);
                const today = new Date().toDateString();

                // Only load if it's from today
                if (progress.date === today) {
                    completedActivities = new Set(progress.activities);
                    completedResponsibilities = new Set(progress.responsibilities);

                    // Update UI
                    generateSchedule();
                    generateResponsibilities();
                    updateProgress();
                }
            }
        }

        // Reset progress
        function resetProgress() {
            if (confirm('Sigur vrei să resetezi tot progresul de astăzi?')) {
                completedActivities.clear();
                completedResponsibilities.clear();
                localStorage.removeItem('childScheduleProgress');

                generateSchedule();
                generateResponsibilities();
                updateProgress();

                alert('Progresul a fost resetat!');
            }
        }

        // Open Add Activity Modal
        function openAddActivityModal() {
            document.getElementById('activity-modal-title').textContent = '➕ Adaugă Activitate Nouă';
            document.getElementById('activity-submit-btn').textContent = '💾 Salvează Activitatea';
            document.getElementById('add-activity-modal').classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling

            // Reset form
            document.getElementById('add-activity-form').reset();
            document.getElementById('conflict-warning').classList.remove('show');

            // Clear edit mode data
            document.getElementById('add-activity-form').removeAttribute('data-edit-mode');
            document.getElementById('add-activity-form').removeAttribute('data-edit-timeslot');
            document.getElementById('add-activity-form').removeAttribute('data-edit-day');

            // Update day checkboxes visual state
            updateDayCheckboxes();
            updateTypeSelection();
        }

        // Open Edit Activity Modal
        function openEditActivityModal(timeSlot, day, activity) {
            document.getElementById('activity-modal-title').textContent = '✏️ Editează Activitatea';
            document.getElementById('activity-submit-btn').textContent = '💾 Salvează Modificările';
            document.getElementById('add-activity-modal').classList.add('active');
            document.body.style.overflow = 'hidden';

            // Set edit mode data
            const form = document.getElementById('add-activity-form');
            form.setAttribute('data-edit-mode', 'true');
            form.setAttribute('data-edit-timeslot', timeSlot);
            form.setAttribute('data-edit-day', day);

            // Parse time slot
            const [startTime, endTime] = timeSlot.split(' - ');

            // Populate form with current values
            document.getElementById('activity-name').value = activity.text;
            document.getElementById('start-time').value = startTime;
            document.getElementById('end-time').value = endTime;

            // Clear all day checkboxes first
            document.querySelectorAll('.day-checkbox input').forEach(cb => cb.checked = false);

            // Check only the current day
            const dayCheckbox = document.querySelector(`.day-checkbox input[value="${day}"]`);
            if (dayCheckbox) {
                dayCheckbox.checked = true;
            }

            // Set activity type
            const typeRadio = document.querySelector(`input[name="activity-type"][value="${activity.type}"]`);
            if (typeRadio) {
                typeRadio.checked = true;
            }

            // Update visual states
            updateDayCheckboxes();
            updateTypeSelection();

            // Clear conflict warning
            document.getElementById('conflict-warning').classList.remove('show');
        }

        // Close Add Activity Modal
        function closeAddActivityModal() {
            document.getElementById('add-activity-modal').classList.remove('active');
            document.body.style.overflow = 'auto'; // Restore scrolling
        }

        // Update visual state of day checkboxes
        function updateDayCheckboxes() {
            document.querySelectorAll('.day-checkbox').forEach(label => {
                const checkbox = label.querySelector('input[type="checkbox"]');
                if (checkbox.checked) {
                    label.classList.add('selected');
                } else {
                    label.classList.remove('selected');
                }
            });
        }

        // Update visual state of activity type selection
        function updateTypeSelection() {
            document.querySelectorAll('.type-option').forEach(label => {
                const radio = label.querySelector('input[type="radio"]');
                if (radio.checked) {
                    label.classList.add('selected');
                } else {
                    label.classList.remove('selected');
                }
            });
        }

        // Check for time conflicts
        function checkTimeConflicts(startTime, endTime, selectedDays) {
            const conflicts = [];
            const newStart = timeToMinutes(startTime);
            const newEnd = timeToMinutes(endTime);

            selectedDays.forEach(day => {
                // Check existing day schedule for conflicts
                const daySchedule = CONFIG.daySchedules[day];
                if (daySchedule && daySchedule.timeSlots) {
                    daySchedule.timeSlots.forEach(slot => {
                        const existingTimeSlot = slot.time;
                        console.log('Checking existing time slot:', existingTimeSlot);
                        const timeParts = existingTimeSlot.split(' - ');
                        console.log('Time parts:', timeParts);

                        if (timeParts.length !== 2) {
                            console.error('Invalid time slot format:', existingTimeSlot);
                            return;
                        }

                        const [existingStart, existingEnd] = timeParts;
                        console.log('Existing start:', existingStart, 'Existing end:', existingEnd);
                        const existingStartMin = timeToMinutes(existingStart);
                        const existingEndMin = timeToMinutes(existingEnd);

                        // Check for overlap
                        if (!(newEnd <= existingStartMin || newStart >= existingEndMin)) {
                            conflicts.push({
                                day: day,
                                time: existingTimeSlot,
                                activity: slot.activity.text
                            });
                        }
                    });
                }
            });

            return conflicts;
        }

        // Convert time string to minutes for comparison
        function timeToMinutes(timeStr) {
            if (!timeStr || typeof timeStr !== 'string') {
                console.error('Invalid time string:', timeStr);
                return 0;
            }
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes;
        }

        // Display conflict warning
        function displayConflictWarning(conflicts) {
            const warningDiv = document.getElementById('conflict-warning');
            const conflictList = document.getElementById('conflict-list');

            if (conflicts.length > 0) {
                conflictList.innerHTML = '';
                conflicts.forEach(conflict => {
                    const li = document.createElement('li');
                    li.textContent = `${conflict.day}, ${conflict.time}: ${conflict.activity}`;
                    conflictList.appendChild(li);
                });
                warningDiv.classList.add('show');
            } else {
                warningDiv.classList.remove('show');
            }
        }

        // Show detailed conflict resolution dialog
        function showConflictResolutionDialog(conflicts) {
            let conflictMessage = 'Au fost detectate conflicte de program:\n\n';
            conflicts.forEach(conflict => {
                conflictMessage += `• ${conflict.day}, ${conflict.time}: ${conflict.activity}\n`;
            });
            conflictMessage += '\n⚠️ Dacă continui, activitățile conflictuale vor fi ȘTERSE AUTOMAT și înlocuite cu noua activitate.\n\n';
            conflictMessage += 'Vrei să continui și să suprascrii aceste activități?';

            return confirm(conflictMessage);
        }

        // Remove conflicting activities automatically
        function removeConflictingActivities(conflicts, newStartTime, newEndTime, newDays) {
            console.log('Removing conflicting activities:', conflicts);
            const removedActivities = [];
            const newStart = timeToMinutes(newStartTime);
            const newEnd = timeToMinutes(newEndTime);

            conflicts.forEach(conflict => {
                const daySchedule = CONFIG.daySchedules[conflict.day];
                if (!daySchedule || !daySchedule.timeSlots) return;

                // Find and remove the conflicting activity
                const slotIndex = daySchedule.timeSlots.findIndex(slot =>
                    slot.time === conflict.time && slot.activity.text === conflict.activity
                );

                if (slotIndex !== -1) {
                    const removedSlot = daySchedule.timeSlots[slotIndex];

                    // Check if we need to partially remove or completely remove the activity
                    const [existingStart, existingEnd] = conflict.time.split(' - ');
                    const existingStartMin = timeToMinutes(existingStart);
                    const existingEndMin = timeToMinutes(existingEnd);

                    // Remove the conflicting activity completely
                    daySchedule.timeSlots.splice(slotIndex, 1);

                    // Remove from completed activities if it was completed
                    const activityId = `${conflict.time}-${conflict.day}`;
                    completedActivities.delete(activityId);

                    removedActivities.push({
                        day: conflict.day,
                        time: conflict.time,
                        activity: conflict.activity
                    });

                    // If the existing activity extends beyond the new activity, create partial activities
                    if (existingStartMin < newStart) {
                        // Create activity for the time before the new activity
                        const beforeEndTime = minutesToTime(newStart);
                        const beforeTimeSlot = `${existingStart} - ${beforeEndTime}`;
                        daySchedule.timeSlots.push({
                            time: beforeTimeSlot,
                            activity: { ...removedSlot.activity } // Create a copy to avoid reference issues
                        });
                        console.log(`Created partial activity before: ${beforeTimeSlot}`);
                    }

                    if (existingEndMin > newEnd) {
                        // Create activity for the time after the new activity
                        const afterStartTime = minutesToTime(newEnd);
                        const afterTimeSlot = `${afterStartTime} - ${existingEnd}`;
                        daySchedule.timeSlots.push({
                            time: afterTimeSlot,
                            activity: { ...removedSlot.activity } // Create a copy to avoid reference issues
                        });
                        console.log(`Created partial activity after: ${afterTimeSlot}`);
                    }

                    // Sort time slots by start time
                    daySchedule.timeSlots.sort((a, b) => {
                        const aStart = timeToMinutes(a.time.split(' - ')[0]);
                        const bStart = timeToMinutes(b.time.split(' - ')[0]);
                        return aStart - bStart;
                    });
                }
            });

            // Show feedback about removed activities
            if (removedActivities.length > 0) {
                let feedbackMessage = '🔄 Rezolvare Conflicte - Următoarele activități au fost modificate/șterse:\n\n';
                removedActivities.forEach(removed => {
                    feedbackMessage += `• ${removed.day}, ${removed.time}: ${removed.activity}\n`;
                });
                feedbackMessage += '\n💡 Dacă activitățile șterse se extindeau în afara noului interval, părțile care nu se suprapun au fost păstrate automat.';

                // Show feedback after a short delay to let the new activity be added first
                setTimeout(() => {
                    alert('✅ Activitatea a fost adăugată cu succes!\n\n' + feedbackMessage);
                }, 100);
            }

            return removedActivities;
        }

        // Convert minutes back to time string (HH:MM format)
        function minutesToTime(minutes) {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
        }

        // Handle form submission
        function handleAddActivitySubmit(event) {
            console.log('Form submit handler called!');
            event.preventDefault();

            const form = document.getElementById('add-activity-form');
            const isEditMode = form.hasAttribute('data-edit-mode');
            const editTimeSlot = form.getAttribute('data-edit-timeslot');
            const editDay = form.getAttribute('data-edit-day');

            const activityName = document.getElementById('activity-name').value.trim();
            const startTime = document.getElementById('start-time').value;
            const endTime = document.getElementById('end-time').value;

            // Debug checkbox selection
            console.log('Checking for day checkboxes...');
            const dayCheckboxes = document.querySelectorAll('.day-checkbox input');
            console.log('Found day checkboxes:', dayCheckboxes.length);
            const checkedBoxes = document.querySelectorAll('.day-checkbox input:checked');
            console.log('Found checked boxes:', checkedBoxes.length);

            const selectedDays = Array.from(checkedBoxes).map(cb => cb.value);
            console.log('Selected days:', selectedDays);

            const activityTypeElement = document.querySelector('input[name="activity-type"]:checked');
            console.log('Activity type element:', activityTypeElement);
            const activityType = activityTypeElement ? activityTypeElement.value : null;
            console.log('Activity type:', activityType);

            // Validation with debugging
            console.log('Starting validation...');
            console.log('Activity name:', activityName);
            console.log('Start time:', startTime);
            console.log('End time:', endTime);
            console.log('Selected days count:', selectedDays.length);
            console.log('Activity type:', activityType);

            if (!activityName) {
                console.log('VALIDATION FAILED: Activity name is empty');
                alert('Te rog introdu numele activității!');
                return;
            }

            if (!startTime || !endTime) {
                console.log('VALIDATION FAILED: Missing time values');
                alert('Te rog selectează ora de început și sfârșit!');
                return;
            }

            if (timeToMinutes(startTime) >= timeToMinutes(endTime)) {
                console.log('VALIDATION FAILED: Start time >= End time');
                alert('Ora de început trebuie să fie înainte de ora de sfârșit!');
                return;
            }

            if (selectedDays.length === 0) {
                console.log('VALIDATION FAILED: No days selected');
                alert('Te rog selectează cel puțin o zi!');
                return;
            }

            if (!activityType) {
                console.log('VALIDATION FAILED: No activity type selected');
                alert('Te rog selectează tipul activității!');
                return;
            }

            console.log('All validation passed!');

            // Check for conflicts (skip current activity if editing)
            let conflicts = checkTimeConflicts(startTime, endTime, selectedDays);

            if (isEditMode) {
                // Filter out the current activity being edited from conflicts
                conflicts = conflicts.filter(conflict =>
                    !(conflict.time === editTimeSlot && conflict.day === editDay)
                );
            }

            if (conflicts.length > 0) {
                console.log('Conflicts detected:', conflicts);
                const proceed = showConflictResolutionDialog(conflicts);
                if (!proceed) {
                    console.log('User declined to resolve conflicts');
                    displayConflictWarning(conflicts);
                    return;
                }
                console.log('User confirmed conflict resolution');
                // If user confirmed, remove conflicting activities
                removeConflictingActivities(conflicts, startTime, endTime, selectedDays);
            }

            // Save or update the activity
            if (isEditMode) {
                updateActivity(editTimeSlot, editDay, activityName, startTime, endTime, selectedDays, activityType);
            } else {
                saveNewActivity(activityName, startTime, endTime, selectedDays, activityType, conflicts.length > 0);
            }
        }

        // Save new activity to schedule
        function saveNewActivity(name, startTime, endTime, days, type, hasConflicts = false) {
            const timeSlot = `${startTime} - ${endTime}`;

            // Add activity to selected days
            days.forEach(day => {
                // Initialize day schedule if it doesn't exist
                if (!CONFIG.daySchedules[day]) {
                    CONFIG.daySchedules[day] = { timeSlots: [] };
                }

                // Add the new time slot to the day
                CONFIG.daySchedules[day].timeSlots.push({
                    time: timeSlot,
                    activity: {
                        text: name,
                        type: type
                    }
                });

                // Sort time slots by start time
                CONFIG.daySchedules[day].timeSlots.sort((a, b) => {
                    const aStart = timeToMinutes(a.time.split(' - ')[0]);
                    const bStart = timeToMinutes(b.time.split(' - ')[0]);
                    return aStart - bStart;
                });
            });

            // Save to localStorage
            localStorage.setItem('childScheduleConfig', JSON.stringify(CONFIG));

            // Regenerate schedule display
            generateSchedule();

            // Close modal
            closeAddActivityModal();

            // Show success message only if there were no conflicts
            // (conflict resolution will show its own detailed message)
            if (!hasConflicts) {
                alert(`Activitatea "${name}" a fost adăugată cu succes!`);
            }
        }

        // Update existing activity
        function updateActivity(oldTimeSlot, oldDay, name, startTime, endTime, days, type) {
            const newTimeSlot = `${startTime} - ${endTime}`;

            // Remove old activity from the old day
            const oldDaySchedule = CONFIG.daySchedules[oldDay];
            if (oldDaySchedule && oldDaySchedule.timeSlots) {
                const oldSlotIndex = oldDaySchedule.timeSlots.findIndex(slot => slot.time === oldTimeSlot);
                if (oldSlotIndex !== -1) {
                    oldDaySchedule.timeSlots.splice(oldSlotIndex, 1);
                }
            }

            // Add new activity to selected days
            days.forEach(day => {
                // Initialize day schedule if it doesn't exist
                if (!CONFIG.daySchedules[day]) {
                    CONFIG.daySchedules[day] = { timeSlots: [] };
                }

                // Add the new time slot to the day
                CONFIG.daySchedules[day].timeSlots.push({
                    time: newTimeSlot,
                    activity: {
                        text: name,
                        type: type
                    }
                });

                // Sort time slots by start time
                CONFIG.daySchedules[day].timeSlots.sort((a, b) => {
                    const aStart = timeToMinutes(a.time.split(' - ')[0]);
                    const bStart = timeToMinutes(b.time.split(' - ')[0]);
                    return aStart - bStart;
                });
            });

            // Update completion status if activity moved
            const oldActivityId = `${oldTimeSlot}-${oldDay}`;
            if (completedActivities.has(oldActivityId)) {
                completedActivities.delete(oldActivityId);
                // Add to new days if it was completed
                days.forEach(day => {
                    const newActivityId = `${newTimeSlot}-${day}`;
                    completedActivities.add(newActivityId);
                });
            }

            // Save to localStorage
            localStorage.setItem('childScheduleConfig', JSON.stringify(CONFIG));

            // Regenerate schedule display
            generateSchedule();

            // Close modal
            closeAddActivityModal();

            // Show success message
            alert(`Activitatea "${name}" a fost modificată cu succes!`);
        }

        // Remove activity
        function removeActivity(timeSlot, day) {
            const daySchedule = CONFIG.daySchedules[day];
            if (!daySchedule || !daySchedule.timeSlots) return;

            const slotIndex = daySchedule.timeSlots.findIndex(slot => slot.time === timeSlot);
            if (slotIndex === -1) return;

            const activity = daySchedule.timeSlots[slotIndex].activity;
            const confirmMessage = `Sigur vrei să ștergi activitatea "${activity.text}" din ${day}, ${timeSlot}?`;
            if (!confirm(confirmMessage)) return;

            // Remove from day schedule
            daySchedule.timeSlots.splice(slotIndex, 1);

            // Remove from completed activities
            const activityId = `${timeSlot}-${day}`;
            completedActivities.delete(activityId);

            // Save to localStorage
            localStorage.setItem('childScheduleConfig', JSON.stringify(CONFIG));

            // Regenerate schedule display
            generateSchedule();
            updateProgress();
            saveProgress();

            // Show success message
            alert(`Activitatea "${activity.text}" a fost ștearsă cu succes!`);
        }

        // Open Edit Responsibility Modal
        function openEditResponsibilityModal(index) {
            const responsibility = CONFIG.responsibilities[index];
            if (!responsibility) return;

            document.getElementById('edit-responsibility-modal').classList.add('active');
            document.body.style.overflow = 'hidden';

            // Populate form
            document.getElementById('edit-resp-index').value = index;
            document.getElementById('edit-resp-text').value = responsibility.text;
            document.getElementById('edit-resp-frequency').value = responsibility.frequency;
            document.getElementById('edit-resp-type').value = responsibility.type;
        }

        // Close Edit Responsibility Modal
        function closeEditResponsibilityModal() {
            document.getElementById('edit-responsibility-modal').classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Handle Edit Responsibility Form Submission
        function handleEditResponsibilitySubmit(event) {
            event.preventDefault();

            const index = parseInt(document.getElementById('edit-resp-index').value);
            const text = document.getElementById('edit-resp-text').value.trim();
            const frequency = document.getElementById('edit-resp-frequency').value;
            const type = document.getElementById('edit-resp-type').value;

            if (!text) {
                alert('Te rog introdu numele responsabilității!');
                return;
            }

            // Update responsibility
            CONFIG.responsibilities[index] = {
                text: text,
                frequency: frequency,
                type: type
            };

            // Save to localStorage
            localStorage.setItem('childScheduleConfig', JSON.stringify(CONFIG));

            // Regenerate responsibilities display
            generateResponsibilities();

            // Close modal
            closeEditResponsibilityModal();

            // Show success message
            alert(`Responsabilitatea "${text}" a fost modificată cu succes!`);
        }

        // Remove responsibility
        function removeResponsibility(index) {
            const responsibility = CONFIG.responsibilities[index];
            if (!responsibility) return;

            const confirmMessage = `Sigur vrei să ștergi responsabilitatea "${responsibility.text}"?`;
            if (!confirm(confirmMessage)) return;

            // Remove responsibility
            CONFIG.responsibilities.splice(index, 1);

            // Remove from completed responsibilities (adjust indices)
            const newCompletedResponsibilities = new Set();
            completedResponsibilities.forEach(respId => {
                const respIndex = parseInt(respId);
                if (respIndex < index) {
                    // Keep responsibilities before the deleted one
                    newCompletedResponsibilities.add(respId);
                } else if (respIndex > index) {
                    // Adjust indices for responsibilities after the deleted one
                    newCompletedResponsibilities.add((respIndex - 1).toString());
                }
                // Skip the deleted responsibility (respIndex === index)
            });
            completedResponsibilities = newCompletedResponsibilities;

            // Save to localStorage
            localStorage.setItem('childScheduleConfig', JSON.stringify(CONFIG));

            // Regenerate responsibilities display
            generateResponsibilities();
            updateProgress();
            saveProgress();

            // Show success message
            alert(`Responsabilitatea "${responsibility.text}" a fost ștearsă cu succes!`);
        }

        // Mode Management Functions
        function loadMode() {
            const savedMode = localStorage.getItem('childScheduleMode');
            isAdminMode = savedMode === 'admin';
        }

        function saveMode() {
            localStorage.setItem('childScheduleMode', isAdminMode ? 'admin' : 'user');
        }

        function updateModeDisplay() {
            const body = document.body;
            const modeToggle = document.getElementById('mode-toggle');
            const modeIcon = document.getElementById('mode-icon');
            const modeText = document.getElementById('mode-text');

            if (isAdminMode) {
                body.classList.remove('user-mode');
                body.classList.add('admin-mode');
                modeToggle.classList.add('admin-mode');
                modeIcon.textContent = '⚙️';
                modeText.textContent = 'Mod Administrator';
            } else {
                body.classList.remove('admin-mode');
                body.classList.add('user-mode');
                modeToggle.classList.remove('admin-mode');
                modeIcon.textContent = '👤';
                modeText.textContent = 'Mod Utilizator';
            }
        }

        function toggleMode() {
            if (isAdminMode) {
                // Switch to user mode
                isAdminMode = false;
                saveMode();
                updateModeDisplay();
                alert('🔒 Modul Utilizator activat!\nFuncțiile de editare sunt acum ascunse.');
            } else {
                // Request password for admin mode
                openPasswordModal();
            }
        }

        function openPasswordModal() {
            document.getElementById('password-modal').classList.add('active');
            document.body.style.overflow = 'hidden';
            document.getElementById('password-input').value = '';
            document.getElementById('password-input').focus();
        }

        function closePasswordModal() {
            document.getElementById('password-modal').classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        function checkPassword() {
            const enteredPassword = document.getElementById('password-input').value;

            if (enteredPassword === ADMIN_PASSWORD) {
                isAdminMode = true;
                saveMode();
                updateModeDisplay();
                closePasswordModal();
                alert('🔓 Modul Administrator activat!\nAcum poți edita și configura programul.');
            } else {
                alert('❌ Parolă incorectă!\nÎncearcă din nou.');
                document.getElementById('password-input').value = '';
                document.getElementById('password-input').focus();
            }
        }









        // Save settings to localStorage
        function saveSettings() {
            localStorage.setItem('childScheduleConfig', JSON.stringify(CONFIG));
        }

        // Load settings from localStorage
        function loadSettings() {
            const saved = localStorage.getItem('childScheduleConfig');
            if (saved) {
                const savedConfig = JSON.parse(saved);

                // Handle migration from old structure to new structure
                if (savedConfig.schedule && savedConfig.timeSlots && !savedConfig.daySchedules) {
                    console.log('Migrating from old schedule structure to new day-specific structure...');
                    // Convert old structure to new structure
                    savedConfig.daySchedules = {};
                    CONFIG.days.forEach(day => {
                        savedConfig.daySchedules[day] = { timeSlots: [] };
                    });

                    // Convert old schedule data
                    Object.keys(savedConfig.schedule).forEach(timeSlot => {
                        CONFIG.days.forEach(day => {
                            if (savedConfig.schedule[timeSlot][day]) {
                                savedConfig.daySchedules[day].timeSlots.push({
                                    time: timeSlot,
                                    activity: savedConfig.schedule[timeSlot][day]
                                });
                            }
                        });
                    });

                    // Sort time slots for each day
                    CONFIG.days.forEach(day => {
                        savedConfig.daySchedules[day].timeSlots.sort((a, b) => {
                            const aStart = timeToMinutes(a.time.split(' - ')[0]);
                            const bStart = timeToMinutes(b.time.split(' - ')[0]);
                            return aStart - bStart;
                        });
                    });

                    // Remove old structure properties
                    delete savedConfig.schedule;
                    delete savedConfig.timeSlots;

                    console.log('Migration completed. New structure:', savedConfig.daySchedules);
                }

                Object.assign(CONFIG, savedConfig);

                // Update UI
                document.getElementById('main-title').textContent = CONFIG.title;
                document.getElementById('subtitle').textContent = CONFIG.subtitle;
                document.getElementById('page-title').textContent = CONFIG.title;

                // Show instructions if available
                if (CONFIG.customInstructions && CONFIG.customInstructions.trim()) {
                    document.getElementById('custom-instructions').textContent = CONFIG.customInstructions;
                    document.getElementById('instructions-panel').style.display = 'block';
                }
            }
        }

        // Initialize event listeners
        function initEventListeners() {
            // Form submission
            const form = document.getElementById('add-activity-form');
            if (form) {
                console.log('Form found, adding submit listener...');
                form.addEventListener('submit', handleAddActivitySubmit);
                console.log('Submit listener added successfully!');
            } else {
                console.error('Form with id "add-activity-form" not found!');
            }

            // Edit responsibility form submission
            const editRespForm = document.getElementById('edit-responsibility-form');
            if (editRespForm) {
                editRespForm.addEventListener('submit', handleEditResponsibilitySubmit);
            }

            // Also add click handler to submit button as backup
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                console.log('Submit button found, adding click listener...');
                submitBtn.addEventListener('click', function(e) {
                    console.log('Submit button clicked!');
                    // Let the form handle the submit event naturally
                });
            } else {
                console.error('Submit button not found!');
            }

            // Day checkbox changes
            document.querySelectorAll('.day-checkbox input').forEach(checkbox => {
                checkbox.addEventListener('change', updateDayCheckboxes);
            });

            // Activity type changes
            document.querySelectorAll('input[name="activity-type"]').forEach(radio => {
                radio.addEventListener('change', updateTypeSelection);
            });

            // Time input changes for conflict detection
            document.getElementById('start-time').addEventListener('change', checkForConflictsOnTimeChange);
            document.getElementById('end-time').addEventListener('change', checkForConflictsOnTimeChange);

            // Close modal when clicking outside
            document.getElementById('add-activity-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeAddActivityModal();
                }
            });

            // Close edit responsibility modal when clicking outside
            document.getElementById('edit-responsibility-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeEditResponsibilityModal();
                }
            });

            // Close password modal when clicking outside
            document.getElementById('password-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closePasswordModal();
                }
            });

            // Password input enter key
            document.getElementById('password-input').addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    checkPassword();
                }
            });

            // Escape key to close modals, Ctrl+P for print
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeAddActivityModal();
                    closeEditResponsibilityModal();
                    closePasswordModal();
                }

                // Ctrl+P for print (override default to use our custom print function)
                if (e.ctrlKey && e.key === 'p') {
                    e.preventDefault();
                    printSchedule();
                }
            });
        }

        // Check for conflicts when time changes
        function checkForConflictsOnTimeChange() {
            const startTime = document.getElementById('start-time').value;
            const endTime = document.getElementById('end-time').value;
            const selectedDays = Array.from(document.querySelectorAll('.day-checkbox input:checked')).map(cb => cb.value);

            if (startTime && endTime && selectedDays.length > 0) {
                const conflicts = checkTimeConflicts(startTime, endTime, selectedDays);
                displayConflictWarning(conflicts);
            } else {
                document.getElementById('conflict-warning').classList.remove('show');
            }
        }

        // Print functionality
        function printSchedule() {
            // Add a print-specific title with current date
            const currentDate = new Date().toLocaleDateString('ro-RO', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            const currentTime = new Date().toLocaleTimeString('ro-RO', {
                hour: '2-digit',
                minute: '2-digit'
            });

            // Store original title
            const originalTitle = document.title;
            const originalSubtitle = document.getElementById('subtitle').textContent;

            // Update title for print
            document.title = `${CONFIG.title} - ${currentDate}`;
            document.getElementById('subtitle').textContent = `${originalSubtitle} - Tipărit pe ${currentDate}`;

            // Update print date in the print-only header
            const printDateElement = document.getElementById('print-date');
            if (printDateElement) {
                printDateElement.textContent = `${currentDate} la ${currentTime}`;
            }

            // Show a brief message before printing
            const printConfirm = confirm('Doriți să tipăriți orarul? Asigurați-vă că imprimanta este pregătită.');

            if (printConfirm) {
                // Trigger browser print dialog
                window.print();
            }

            // Restore original title after print dialog
            setTimeout(() => {
                document.title = originalTitle;
                document.getElementById('subtitle').textContent = originalSubtitle;
            }, 1000);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing app...');
            initApp();
            initEventListeners();
            console.log('App initialization complete!');
        });
    </script>
</body>
</html>